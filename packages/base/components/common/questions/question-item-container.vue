<script setup lang="ts">
import QSingleChoice from './q-single-choice'

defineOptions({
  name: 'QuestionItemContainer',
})
const props = defineProps<Props>()

interface Props {
  type?: Question.QuestionType
}
interface QuestionModule {
  label: string
  component: Component
}
const itemInfo = defineModel<Question.ProcessedQuestionData>('itemInfo', {
  default: () => ({}),
})
const moduleMap: Partial<Record<Question.QuestionModule, QuestionModule>> = {
  'single-choice': { label: '单选题', component: QSingleChoice },
  // MultipleChoice: { label: '多选题', component: QMultipleChoice },
  // TrueFalse: { label: '判断题', component: QTrueFalse },
  // FillBlank: { label: '填空题', component: QFillBlank },
}

const activeModule = computed(() => {
  const componentName = itemInfo.value.componentsName || 'single-choice'
  return moduleMap[componentName as Question.QuestionModule] || moduleMap['single-choice']
})
</script>

<template>
  <div>
    <component
      :is="activeModule.component"
      v-if="activeModule"
      :item="itemInfo"
      :type="props.type"
    />
    <!-- 如果组件不存在，显示提示信息 -->
    <div v-if="!activeModule" class="py-4 text-center text-gray-500">
      <p>题型组件 "{{ itemInfo.componentsName }}" 暂未实现</p>
      <p class="mt-2 text-sm">
        当前使用单选题组件作为默认显示
      </p>
    </div>
  </div>
</template>
